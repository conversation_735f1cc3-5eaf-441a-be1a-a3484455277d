<script lang="ts">
	import { page } from '$app/state';

	let pillIndicatorElement: HTMLSpanElement;

	const navItems = [
		{ href: '/', label: 'Home' },
		{ href: '/o-nas', label: 'O nás' },
		{ href: '/cenik', label: '<PERSON>n<PERSON>' },
		{ href: '/kontakt', label: 'Kontakt' },
		{ href: '/stan-se-lektorem', label: 'Staň se lektorem' }
	];

	function updatePillIndicator(pathname: string) {
		const activeLink = document.querySelector(`a[href="${pathname}"]`);
		if (!activeLink) return;
	}

	$: if (pillIndicatorElement) updatePillIndicator(page.url.pathname);
</script>

<nav class="header-nav">
	{#each navItems as item}
		<a href={item.href} class:active={item.href === page.url.pathname}>
			{item.label}
		</a>
	{/each}

	<span class="pill-indicator" bind:this={pillIndicatorElement}></span>
</nav>

<style lang="scss">
	.header-nav {
		position: relative;
		width: 100%;
		background-color: var(--color-dark);
		height: var(--header-height);
		border-radius: calc(var(--header-height) / 2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		isolation: isolate;
		padding: 0 var(--spacing-m);

		--pill-height: calc(var(--header-height) - var(--spacing-s) * 2);

		a {
			color: var(--color-light);
			text-decoration: none;
			padding: 0 var(--spacing-m);

			&.active {
				color: var(--color-dark);
			}
		}

		.pill-indicator {
			position: absolute;
			top: 50%;
			left: 0;
			width: 50px;
			z-index: -1;
			height: var(--pill-height);
			border-radius: calc(var(--pill-height) / 2);
			background-color: var(--color-light);
		}
	}
</style>
